import pandas as pd
import re

def parse_responsibility_sales(sales_str):
    """
    解析责任销售字符串，提取姓名和比例
    例如: "吴何梅27.59%，吴俊廷72.41%" -> [("吴何梅", 0.2759), ("吴俊廷", 0.7241)]
    """
    if pd.isna(sales_str) or sales_str.strip() == "":
        return []
    
    # 使用正则表达式匹配姓名和百分比
    pattern = r'([^，,\d%]+?)(\d+\.?\d*)%'
    matches = re.findall(pattern, sales_str)
    
    result = []
    for name, percentage in matches:
        name = name.strip()
        percentage = float(percentage) / 100
        result.append((name, percentage))
    
    return result

def process_excel_data(input_file, output_file):
    """
    处理Excel数据，将F列的责任销售信息拆分成多行
    """
    # 读取Excel文件
    df = pd.read_excel(input_file)
    
    # 获取列名
    columns = df.columns.tolist()
    
    # 找到责任销售列的索引（假设是第6列，索引为5）
    responsibility_sales_col_idx = 5  # F列
    responsibility_sales_col = columns[responsibility_sales_col_idx]
    
    # 创建新的列名列表
    new_columns = columns.copy()
    # 在责任销售列后插入新的责任比例列
    new_columns.insert(responsibility_sales_col_idx + 1, "责任比例")
    # 在订单财报利润列后插入分摊利润列
    profit_col_idx = new_columns.index("订单财报利润")
    new_columns.insert(profit_col_idx + 1, "分摊利润")
    
    # 创建新的数据列表
    new_data = []
    
    for index, row in df.iterrows():
        # 获取责任销售信息
        sales_info = row[responsibility_sales_col]
        
        # 解析责任销售信息
        parsed_sales = parse_responsibility_sales(str(sales_info))
        
        if not parsed_sales:
            # 如果没有解析到销售信息，保持原行
            new_row = row.tolist()
            new_row.insert(responsibility_sales_col_idx + 1, "")  # 责任比例
            new_row.insert(profit_col_idx + 1, "")  # 分摊利润
            new_data.append(new_row)
        else:
            # 获取订单财报利润
            order_profit = row["订单财报利润"]
            if pd.isna(order_profit):
                order_profit = 0
            
            # 为每个销售人员创建一行
            for name, percentage in parsed_sales:
                new_row = row.tolist()
                
                # 更新责任销售姓名为单个人员
                new_row[responsibility_sales_col_idx] = name
                
                # 插入责任比例
                percentage_str = f"{percentage:.2%}"
                new_row.insert(responsibility_sales_col_idx + 1, percentage_str)
                
                # 计算分摊利润
                allocated_profit = order_profit * percentage
                new_row.insert(profit_col_idx + 1, round(allocated_profit, 2))
                
                new_data.append(new_row)
    
    # 创建新的DataFrame
    new_df = pd.DataFrame(new_data, columns=new_columns)
    
    # 保存到新的Excel文件
    new_df.to_excel(output_file, index=False)
    print(f"处理完成，结果已保存到: {output_file}")
    
    return new_df

def process_sample_data():
    """
    处理示例数据
    """
    # 创建示例数据
    sample_data = {
        "月份": ["6月"],
        "序列": [1],
        "（非责任销售）姓名": ["邓小聪"],
        "易仓发货SKU": ["TTCUTV002PK"],
        "订单号（PO#）": ["113-8945026-7333008"],
        "（责任销售）姓名": ["吴何梅27.59%，吴俊廷72.41%"],
        "订单财报利润": [-174.99],
        "分摊（财务填写）": [""],
        "亚马逊售价（USD）+佣金率（如有)": ["退款"],
        "清货通知来源": [""]
    }
    
    df = pd.DataFrame(sample_data)
    
    # 保存示例数据到Excel
    df.to_excel("示例数据.xlsx", index=False)
    print("示例数据已创建: 示例数据.xlsx")
    
    # 处理示例数据
    result_df = process_excel_data("../../示例数据.xlsx", "处理结果.xlsx")
    
    print("\n处理结果预览:")
    print(result_df.to_string(index=False))

if __name__ == "__main__":
    # 处理示例数据
    process_sample_data()
    
    # 如果您有实际的Excel文件，可以使用以下代码：
    # process_excel_data("您的输入文件.xlsx", "输出文件.xlsx")
